import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { getAllBlogPosts } from '../data/blogPosts';
import UnifiedCTA from '../components/UnifiedCTA';
import BlogThumbnail from '../components/BlogThumbnail';

const BlogPage = () => {
  const allBlogPosts = getAllBlogPosts();
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [filteredPosts, setFilteredPosts] = useState(allBlogPosts);

  // Define categories based on the blog post tags
  const categories = [
    'All',
    'Visa Tips',
    'Embassy Requirements',
    'Application Guide',
    'Legal Info'
  ];

  // Filter posts based on selected category
  useEffect(() => {
    if (selectedCategory === 'All') {
      setFilteredPosts(allBlogPosts);
    } else {
      const filtered = allBlogPosts.filter(post =>
        post.tags.some(tag =>
          tag.toLowerCase().includes(selectedCategory.toLowerCase()) ||
          (selectedCategory === 'Application Guide' && (tag.includes('Application') || tag.includes('Guide'))) ||
          (selectedCategory === 'Legal Info' && (tag.includes('Legal') || tag.includes('Information')))
        )
      );
      setFilteredPosts(filtered);
    }
  }, [selectedCategory, allBlogPosts]);

  // Set page title and meta description
  useEffect(() => {
    document.title = 'Visa Resources & Expert Travel Tips | VerifiedOnward';

    // Update meta description
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.name = 'description';
      document.head.appendChild(metaDescription);
    }
    metaDescription.content = 'Expert visa tips and resources for flight reservations, embassy applications, and travel planning. Get the latest guides and advice for successful visa applications.';

    // Cleanup function to reset title when component unmounts
    return () => {
      document.title = 'VerifiedOnward - Embassy-Approved Flight Reservation in 60 Seconds';
    };
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-brand-50/30">
      {/* Redesigned Premium Header Section */}
      <section className="relative py-20 overflow-hidden">
        {/* Subtle Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-brand-500/3 via-blue-500/3 to-accent-500/3"></div>

        {/* Minimal Airplane Trail Background */}
        <div className="absolute inset-0 opacity-[0.02]">
          <div className="absolute top-1/3 left-1/4 w-80 h-0.5 bg-gradient-to-r from-transparent via-brand-400 to-transparent transform rotate-12"></div>
          <div className="absolute top-2/3 right-1/4 w-60 h-0.5 bg-gradient-to-r from-transparent via-accent-400 to-transparent transform -rotate-12"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center max-w-5xl mx-auto"
          >
            {/* Clean Category Badge */}
            <div className="inline-flex items-center bg-brand-100/80 text-brand-700 px-5 py-2 rounded-full text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-brand-500 rounded-full mr-3"></span>
              VISA RESOURCES & EXPERT GUIDES
            </div>

            {/* Improved Typography Hierarchy */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-black text-brand-800 mb-6 leading-tight">
              Expert
              <span className="bg-gradient-to-r from-brand-600 to-accent-600 bg-clip-text text-transparent"> Travel Guides</span>
            </h1>

            <p className="text-xl text-neutral-600 mb-8 leading-relaxed max-w-3xl mx-auto">
              Get insider tips, step-by-step guides, and expert advice to make your visa application process smooth and successful.
              Written by travel experts who've helped 75,000+ travelers secure their visas.
            </p>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center gap-8 mt-8 text-sm text-neutral-600">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-medium">75,000+ Successful Applications</span>
              </div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-brand-100 rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-brand-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-medium">99.7% Embassy Acceptance</span>
              </div>
              <div className="flex items-center">
                <div className="w-8 h-8 bg-accent-100 rounded-full flex items-center justify-center mr-3">
                  <svg className="w-4 h-4 text-accent-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                </div>
                <span className="font-medium">Expert-Verified Content</span>
              </div>
            </div>

            <p className="text-lg md:text-xl text-brand-700 leading-relaxed font-medium mb-8 max-w-3xl mx-auto">
              Professional visa application guides, embassy requirements, and travel tips from industry experts.
              <br className="hidden md:block" />
              <span className="text-brand-800 font-semibold">Everything you need for successful visa applications.</span>
            </p>

            {/* Subtle Trust Tagline */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-center"
            >
              <p className="text-sm text-brand-600 font-medium">
                Expert visa guides trusted by 75,000+ travelers worldwide • 99.7% embassy acceptance
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Category Filter Bar */}
      <section className="py-8 bg-white/50 backdrop-blur-sm border-y border-brand-100">
        <div className="container-modern">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="overflow-x-auto"
          >
            {/* Mobile: Horizontal scroll, Desktop: Centered flex */}
            <div className="flex md:justify-center gap-3 md:gap-4 pb-2 md:pb-0 min-w-max md:min-w-0 px-4 md:px-0">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-6 py-3 rounded-full font-semibold text-sm transition-all duration-300 transform hover:scale-105 whitespace-nowrap ${
                    selectedCategory === category
                      ? 'bg-brand-500 text-white shadow-brand-glow'
                      : 'bg-white text-brand-600 border border-brand-200 hover:bg-brand-50 hover:border-brand-300 shadow-soft'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Redesigned Blog Posts Grid */}
      <section className="py-12">
        <div className="container-modern">
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-10"
          >
            {filteredPosts.map((post) => (
              <motion.article
                key={post.id}
                variants={itemVariants}
                className="premium-card group hover:-translate-y-2 hover:shadow-luxury transition-all duration-300 h-full flex flex-col"
              >
                {/* Blog Thumbnail - Fixed Aspect Ratio */}
                <div className="p-6 pb-0">
                  <div className="aspect-[16/9] w-full">
                    <BlogThumbnail thumbnail={post.thumbnail} title={post.title} />
                  </div>
                </div>

                <div className="p-6 pt-4 flex-1 flex flex-col">
                  {/* Tags - Consistent Spacing */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.slice(0, 3).map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 text-xs font-medium bg-brand-100 text-brand-700 rounded-full border border-brand-200/50 hover:bg-brand-200 transition-colors"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Title - Consistent Height */}
                  <h2 className="text-xl lg:text-2xl font-bold text-neutral-800 mb-4 group-hover:text-brand-600 transition-colors leading-tight line-clamp-3">
                    <Link to={`/blog/${post.slug}`} className="hover:underline">
                      {post.title}
                    </Link>
                  </h2>

                  {/* Meta Information - Aligned */}
                  <div className="flex items-center text-sm text-neutral-500 mb-4">
                    <svg className="w-4 h-4 mr-2 text-brand-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="mr-4 font-medium">{new Date(post.publishDate).toLocaleDateString('en-US', {
                      month: 'short',
                      day: 'numeric',
                      year: 'numeric'
                    })}</span>
                    <svg className="w-4 h-4 mr-2 text-accent-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="font-medium">{post.readTime}</span>
                  </div>

                  {/* Excerpt - Flexible Height */}
                  <p className="text-neutral-600 mb-6 leading-relaxed text-base flex-1 line-clamp-3">
                    {post.excerpt}
                  </p>

                  {/* Read More Button - Aligned at Bottom */}
                  <div className="mt-auto">
                    <Link
                      to={`/blog/${post.slug}`}
                      className="inline-flex items-center bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white font-semibold px-6 py-3 rounded-xl transition-all duration-300 shadow-soft hover:shadow-brand-glow transform hover:scale-105 group"
                    >
                      <span>Read Guide</span>
                      <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </Link>
                  </div>
                </div>
            </motion.article>
          ))}
          </motion.div>

          {/* Most Popular Guides Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mt-20"
          >
            <div className="text-center mb-12">
              <div className="inline-flex items-center bg-accent-100/80 text-accent-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
                <span className="w-2 h-2 bg-accent-500 rounded-full mr-2"></span>
                MOST POPULAR
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-brand-800 mb-4">
                Essential Visa Guides
              </h2>
              <p className="text-lg text-neutral-600 max-w-2xl mx-auto leading-relaxed">
                Our most-read articles that have helped thousands of travelers secure their visas successfully
              </p>
            </div>

            {/* Consistent Grid Container with max-width and center alignment */}
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {allBlogPosts.slice(0, 3).map((post, index) => (
                  <motion.div
                    key={post.id}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="premium-card group hover:-translate-y-2 transition-all duration-300 relative h-full flex flex-col"
                  >
                    {/* Popular Badge */}
                    <div className="absolute -top-3 -right-3 z-10">
                      <div className="bg-accent-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-accent-glow">
                        #{index + 1} Popular
                      </div>
                    </div>

                    <div className="p-6 flex-1 flex flex-col">
                      {/* Fixed Aspect Ratio Thumbnail */}
                      <div className="aspect-[16/9] w-full mb-4">
                        <BlogThumbnail thumbnail={post.thumbnail} title={post.title} />
                      </div>

                      {/* Tags */}
                      <div className="flex flex-wrap gap-2 mb-3">
                        {post.tags.slice(0, 2).map((tag, tagIndex) => (
                          <span
                            key={tagIndex}
                            className="px-3 py-1 text-xs font-medium bg-brand-100 text-brand-700 rounded-full border border-brand-200/50"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>

                      {/* Title with consistent height */}
                      <h4 className="text-lg font-bold text-neutral-800 mb-3 group-hover:text-brand-600 transition-colors leading-tight line-clamp-2 min-h-[3.5rem] flex-1">
                        <Link to={`/blog/${post.slug}`} className="hover:underline">
                          {post.title}
                        </Link>
                      </h4>

                      {/* Read Time */}
                      <div className="flex items-center text-sm text-neutral-500 mb-4">
                        <svg className="w-4 h-4 mr-2 text-accent-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{post.readTime}</span>
                      </div>

                      {/* Read Button - Aligned at Bottom */}
                      <div className="mt-auto">
                        <Link
                          to={`/blog/${post.slug}`}
                          className="inline-flex items-center text-brand-600 font-semibold hover:text-brand-700 transition-colors group"
                        >
                          <span>Read Guide</span>
                          <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Latest Articles Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="mt-20"
          >
            <div className="text-center mb-12">
              <div className="inline-flex items-center bg-blue-100/80 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-2"></span>
                LATEST UPDATES
              </div>
              <h2 className="text-3xl md:text-4xl font-bold text-brand-800 mb-4">
                Recent Articles
              </h2>
              <p className="text-lg text-neutral-600 max-w-2xl mx-auto leading-relaxed">
                Stay updated with our newest visa guides and travel tips for 2025
              </p>
            </div>

            {/* Consistent Grid Container with max-width and center alignment */}
            <div className="max-w-6xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 justify-items-center">
                {allBlogPosts
                  .sort((a, b) => new Date(b.publishDate) - new Date(a.publishDate))
                  .slice(0, 2)
                  .map((post, index) => (
                    <motion.div
                    key={post.id}
                    initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="premium-card group hover:-translate-y-2 transition-all duration-300 h-full flex flex-col w-full max-w-md"
                  >
                    <div className="p-6 flex-1 flex flex-col">
                      {/* Fixed Aspect Ratio Thumbnail */}
                      <div className="aspect-[16/9] w-full mb-4">
                        <BlogThumbnail thumbnail={post.thumbnail} title={post.title} />
                      </div>

                      {/* Date Badge */}
                      <div className="inline-flex items-center bg-green-100 text-green-700 px-3 py-1 rounded-full text-xs font-semibold mb-3">
                        <span className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></span>
                        New
                      </div>

                      {/* Title with consistent height */}
                      <h4 className="text-lg font-bold text-neutral-800 mb-3 group-hover:text-brand-600 transition-colors leading-tight line-clamp-2 min-h-[3.5rem] flex-1">
                        <Link to={`/blog/${post.slug}`} className="hover:underline">
                          {post.title}
                        </Link>
                      </h4>

                      {/* Meta Information */}
                      <div className="flex items-center text-sm text-neutral-500 mb-4">
                        <svg className="w-4 h-4 mr-2 text-brand-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <span className="mr-4">{new Date(post.publishDate).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })}</span>
                        <svg className="w-4 h-4 mr-2 text-accent-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{post.readTime}</span>
                      </div>

                      {/* Read More Button - Aligned at Bottom */}
                      <div className="mt-auto">
                        <Link
                          to={`/blog/${post.slug}`}
                          className="inline-flex items-center text-brand-600 font-semibold hover:text-brand-700 transition-colors group"
                        >
                          <span>Read Article</span>
                          <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </svg>
                        </Link>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          {/* Soft Call to Action Section - Consistent Width */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center mt-20"
          >
            {/* Match the same max-width as blog grid containers */}
            <div className="max-w-6xl mx-auto">
              <div className="max-w-4xl mx-auto p-8 bg-gradient-to-br from-brand-50/50 to-blue-50/30 rounded-2xl border border-brand-100/50">
                {/* Soft Headline */}
                <div className="flex items-center justify-center mb-4">
                  <span className="text-2xl mr-3">✈️</span>
                  <h3 className="text-2xl md:text-3xl font-bold text-brand-800">
                    Need a visa-approved flight reservation?
                  </h3>
                </div>

                <p className="text-lg text-brand-700 mb-6 leading-relaxed">
                  Get yours in 60 seconds with our embassy-approved service.
                </p>

                {/* Soft CTA Button */}
                <Link
                  to="/"
                  className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-soft hover:shadow-brand-glow transform hover:scale-105 group"
                >
                  <span>Start Now</span>
                  <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>

                {/* Subtle Trust Footer */}
                <div className="mt-4 text-sm text-brand-600">
                  <p>✓ 99.8% Embassy Acceptance • ✓ Instant Download • ✓ Secure Payment</p>
                </div>
              </div>
            </div>
          </motion.div>

          {/* Soft CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mt-20"
          >
            <div className="max-w-4xl mx-auto text-center">
              <div className="bg-gradient-to-br from-brand-50/50 to-blue-50/30 rounded-2xl p-8 border border-brand-100/50">
                <h3 className="text-2xl md:text-3xl font-bold text-brand-800 mb-4">
                  ✅ Ready to apply for your visa?
                </h3>
                <p className="text-lg text-neutral-700 mb-6 leading-relaxed max-w-2xl mx-auto">
                  Get your embassy-compliant flight reservation instantly, trusted by 75,000+ travelers worldwide.
                </p>
                <Link
                  to="/"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-brand-500 to-brand-600 hover:from-brand-600 hover:to-brand-700 text-white font-semibold rounded-xl transition-all duration-300 shadow-soft hover:shadow-brand-glow transform hover:scale-105 group"
                >
                  <span>Generate Your Ticket Now →</span>
                  <svg className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
                <div className="mt-4 text-sm text-brand-600">
                  <p>✓ Secure payment • ✓ Instant download • ✓ Email support</p>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default BlogPage;
